import os
from urllib.parse import urlencode
import streamlit as st
from streamlit.runtime import Runtime
from streamlit.runtime.scriptrunner import get_script_run_ctx
from streamlit_keycloak import login
from streamlit_option_menu import option_menu
import sys
import requests
import importlib.util

st.set_page_config(
    page_title="Vigilex",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)
# Hide Streamlit's default menu and footer
hide_streamlit_style = """
    <style>
        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        header {visibility: hidden;}
    </style>
"""
st.markdown(hide_streamlit_style, unsafe_allow_html=True)
# #Ensure the paths to the pages are added to the system path
# sys.path.append(os.path.abspath(r"C:\Users\<USER>\Downloads\project-main"))
import base64
import os

def set_background(image_filename):
    import base64, os
    image_path = os.path.join(os.path.dirname(__file__), image_filename)
    with open(image_path, "rb") as img_file:
        b64_img = base64.b64encode(img_file.read()).decode()
    st.markdown(
        f"""
        <style>
        [data-testid="stAppViewContainer"] {{
            background: url("data:image/jpeg;base64,{b64_img}") no-repeat center center fixed;
            background-size: cover;
            min-height: 100vh;
        }}
        </style>
        """,
        unsafe_allow_html=True
    )


# 🔥 Make sure this line is placed at the top-level (not indented or inside a condition)


sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import the app functions from each page
from Accueil import app as Accueil_app
from Plans_de_Remédiation import app as Plans_de_Remédiation_app
from Vulnérabilités_et_Correctifs import app as Vulnérabilités_et_Correctifs_app
from Bulletins_sécurité import app as Bulletins_sécurité_app

# Try to import from frontend module first (local development)
# If that fails, try to import directly (production environment)
testingcontre = None
testingCERT = None
testingSOC = None
test = None

try:
    import frontend.testingcontre as testingcontre
    import frontend.testingCERT as testingCERT
    import frontend.testingSOC as testingSOC
    import frontend.test as test
except ImportError:
    # In production, the files are likely in the same directory
    try:
        import testingcontre
        import testingCERT
        import testingSOC
        import test

    except ImportError:
        st.error("Impossible de charger les modules testingcontre et testingCERT")

        # As a fallback, try to load the modules dynamically
        script_dir = os.path.dirname(os.path.abspath(__file__))
        st.info(f"Script directory: {script_dir}")

        # List files in the directory for debugging
        try:
            files_in_dir = os.listdir(script_dir)
            st.info(f"Files in directory: {files_in_dir}")
        except Exception as e:
            st.error(f"Error listing directory: {e}")

        # Try to load testingcontre.py
        testingcontre_path = os.path.join(script_dir, "testingcontre.py")
        if os.path.exists(testingcontre_path):
            spec = importlib.util.spec_from_file_location("testingcontre", testingcontre_path)
            testingcontre = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(testingcontre)
        else:
            st.error(f"Le fichier testingcontre.py n'a pas été trouvé dans {script_dir}")

        # Try to load testingCERT.py
        testingCERT_path = os.path.join(script_dir, "testingCERT.py")
        if os.path.exists(testingCERT_path):
            spec = importlib.util.spec_from_file_location("testingCERT", testingCERT_path)
            testingCERT = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(testingCERT)
        else:
            st.error(f"Le fichier testingCERT.py n'a pas été trouvé dans {script_dir}")
        # Try to load testingSOC.py
        testingSOC_path = os.path.join(script_dir, "testingSOC.py")
        if os.path.exists(testingSOC_path):
            spec = importlib.util.spec_from_file_location("testingSOC", testingSOC_path)
            testingSOC = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(testingSOC)
        else:
            st.error(f"Le fichier testingSOC.py n'a pas été trouvé dans {script_dir}")

        # Try to load test.py
        test_path = os.path.join(script_dir, "test.py")
        if os.path.exists(test_path):
            spec = importlib.util.spec_from_file_location("test", test_path)
            test = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(test)
        else:
            st.error(f"Le fichier test.py n'a pas été trouvé dans {script_dir}")

# Functions to get session info for logout URL
def _get_session_id():
    context = get_script_run_ctx()
    if not context:
        return
    return context.session_id


def _get_current_request():
    session_id = _get_session_id()
    if not session_id:
        return None
    runtime = Runtime._instance
    if not runtime:
        return
    client = runtime.get_client(session_id)
    if not client:
        return
    return client.request


def get_web_origin():
    request = _get_current_request()
    return request.headers["Origin"] if request else os.getenv("WEB_BASE", "")


# Keycloak Configuration
keycloak_endpoint = "https://keycloak-route-enterprise-search.apps.okd-poc.soprahronline.sopra"
keycloak_realm = "testme"

# Force authentication check - clear any invalid session data
if not st.session_state.get("keycloak_user_info") or not st.session_state.get("keycloak_id_token"):
    # Clear any partial session data
    if "keycloak_user_info" in st.session_state:
        del st.session_state["keycloak_user_info"]
    if "keycloak_id_token" in st.session_state:
        del st.session_state["keycloak_id_token"]

    set_background("test2.png")
   

    keycloak = login(
        url=keycloak_endpoint,
        realm=keycloak_realm,
        client_id="testangular",
        init_options={"checkLoginIframe": False},
    )

    if keycloak.authenticated:
        st.session_state.keycloak_id_token = keycloak.id_token
        st.session_state.keycloak_user_info = keycloak.user_info
        st.rerun()  # Refresh the page after successful login
    else:
        st.warning("Authentification requise pour continuer.")
        st.stop()  # Stop execution until authentication is completed




# Show the app content if authenticated

if st.session_state.get("keycloak_user_info"):
    # remove login background when authenticated
    st.markdown(
        """
        <style>
        [data-testid="stAppViewContainer"] {
            background: none !important;
        }
        </style>
        """,
        unsafe_allow_html=True
    )

    # Prepare the logout URL
    params = urlencode(
        {
            "post_logout_redirect_uri": get_web_origin(),
            "id_token_hint": st.session_state.keycloak_id_token,
        }
    )
    logout_url = f"{keycloak_endpoint}/realms/{keycloak_realm}/protocol/openid-connect/logout?{params}"

    # Sidebar and Page Navigation
    with st.sidebar:
        SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))  # Get current script directory
        logo_path = os.path.join(SCRIPT_DIR, "sopra-logo.png")  # Ensure correct path



        st.image(logo_path, use_container_width=True)

        selected_page = option_menu(
            menu_title='Navigation',
            options=[
                '🏠 Accueil',
                '📝 Plans de Remédiation Pentest',
                '⚙️ Vulnérabilités et Correctifs',
                '📝 Bulletins sécurité R&D',
                '📊 Plans de Remédiation Scan Qualys',
                '📄 Bulletins CERT',
                '🛡️ Incidents SOC',
                'test',
      
            ],
            menu_icon='cast',
            default_index=0,
            styles={
                "container": {"padding": "5px!important", "background-color": "#fafafa"},
                "icon": {"display": "none"},
                "nav-link": {"font-size": "20px", "text-align": "left", "margin": "0px", "--hover-color": "#ff6600"},
                "nav-link-selected": {"background-color": "#cc0033"},
            }
        )

        # Logout Button
        if st.button("Déconnexion"):
            st.session_state.clear()  # Clear session state
            st.markdown(
                f'<meta http-equiv="refresh" content="0; url={logout_url}">',
                unsafe_allow_html=True,
            )
            st.stop()  # Stop further execution after logout


    # Load the selected page
    if selected_page == "🏠 Accueil":
        Accueil_app()
    elif selected_page == "📝 Plans de Remédiation Pentest":
        Plans_de_Remédiation_app()
    elif selected_page == "⚙️ Vulnérabilités et Correctifs":
        Vulnérabilités_et_Correctifs_app()
    elif selected_page == "📝 Bulletins sécurité R&D":
        Bulletins_sécurité_app()
    elif selected_page == "📊 Plans de Remédiation Scan Qualys":
        if testingcontre:
            testingcontre.main()
        else:
            st.error("Module testingcontre non disponible")
    elif selected_page == "📄 Bulletins CERT":
        if testingCERT:
            testingCERT.main()
        else:
            st.error("Module testingCERT non disponible")
    elif selected_page == "🛡️ Incidents SOC":
        if testingSOC:
            testingSOC.main()
        else:
            st.error("Module testingSOC non disponible")
    elif selected_page == "test":
        if test:
            test.main()
        else:
            st.error("Module test non disponible")
else:
    # User is not authenticated - this should not happen due to st.stop() above
    # but adding as a safety measure
    st.error("Accès non autorisé. Veuillez vous connecter.")
    st.stop()
